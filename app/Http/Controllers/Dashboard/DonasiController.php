<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Donation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DonasiController extends Controller
{
    public function index(Request $request)
    {
        // Apply the tablePartial method to get filtered data
        $donasi = $this->getFilteredDonations($request);

        return view('dashboard.donasi.index', compact('donasi'));
    }

    /**
     * Get filtered donations based on request parameters
     */
    private function getFilteredDonations(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $query = Donation::with(['program', 'donatur', 'staff', 'moota'])
            ->orderBy('created_at', 'desc');

        // Apply filters if present
        if ($request->filled('donatur')) {
            $query->where('donatur_id', $request->donatur);
        }

        if ($request->filled('program')) {
            $query->where('program_id', $request->program);
        }

        if ($request->filled('penghubung')) {
            // The staff relationship uses user_id as the foreign key
            $query->where('user_id', $request->penghubung);
        }

        if ($request->filled('creation')) {
            $query->whereHas('moota', function ($q) use ($request) {
                $q->where('creation', $request->creation);
            });
        }

        if ($request->filled('manager')) {
            if ($request->manager === 'null') {
                // Filter for donations with no manager
                $query->whereHas('staff', function ($q) {
                    $q->whereNull('manager');
                });
            } else {
                // Filter for donations with the specified manager
                $query->whereHas('staff', function ($q) use ($request) {
                    $q->where('manager', 'ilike', $request->manager);
                });
            }
        }

        $donations = $query->paginate($perPage);

        // Transform the data for the table view
        $donations->getCollection()->transform(function ($item) {
            return [
                'id' => $item->id,
                'amount' => $item->moota ? $item->moota->amount : 0,
                'program' => $item->program ? $item->program->name : '-',
                'donatur' => $item->donatur ? $item->donatur->name : '-',
                'penghubung' => $item->staff ? $item->staff->name : '-',
                'manager' => $item->staff && $item->staff->manager ? $item->staff->manager : '-',
                'date' => $item->created_at ? $item->created_at->format('Y-m-d H:i') : '-',
                'creation' => $item->moota ? $item->moota->creation : '-',
                'is_webhook' => $item->moota && $item->moota->creation === 'webhook',
            ];
        });

        return $donations;
    }

    public function show($id)
    {
        $donasi = Donation::with(['program', 'donatur', 'staff', 'moota'])->findOrFail($id);
        return response()->json($donasi);
    }

    public function edit($id)
    {
        $donasi = Donation::with(['program', 'donatur', 'staff', 'moota'])->findOrFail($id);
        return response()->json($donasi);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'amount' => 'required|numeric',
            'donatur' => 'nullable|array', // either select or create
            'donatur.id' => 'nullable|exists:donaturs,id',
            'donatur.name' => 'nullable|string|max:255',
            'program' => 'nullable|array',
            'program.id' => 'nullable|exists:programs,id',
            'penghubung' => 'nullable|array',
            'penghubung.id' => 'nullable|exists:users,id',
        ]);

        // Handle donatur: selected or create new
        $donaturId = null;
        if (!empty($validated['donatur']['id'])) {
            $donaturId = $validated['donatur']['id'];
        } elseif (!empty($validated['donatur']['name'])) {
            // Check if donatur with this name already exists (case-insensitive)
            $donatur = \App\Models\Donatur::whereRaw('LOWER(name) = ?', [strtolower($validated['donatur']['name'])])->first();
            if (!$donatur) {
                $donatur = \App\Models\Donatur::create(['name' => $validated['donatur']['name']]);
            }
            $donaturId = $donatur->id;
        }

        // Handle program
        $programId = $validated['program']['id'] ?? null;

        // Handle penghubung, default to current user
        $penghubungId = $validated['penghubung']['id'] ?? Auth::user()->id;

        // Create the Moota record
        $moota = \App\Models\Moota::create([
            'mutation_id' => 'manual_' . time() . '_' . rand(1000, 9999),
            'type' => 'CR',
            'person' => $donaturId ? \App\Models\Donatur::find($donaturId)->name : null,
            'amount' => $validated['amount'],
            'creation' => 'manual',
            'date' => now(),
            'note' => 'Manual donation entry',
        ]);

        // Create the Donation
        $donasi = \App\Models\Donation::create([
            'moota_id' => $moota->id,
            'donatur_id' => $donaturId,
            'program_id' => $programId,
            'user_id' => $penghubungId,
            'updated_by' => Auth::user()->id,
        ]);

        return response()->json(['success' => true, 'donasi' => $donasi]);
    }

    public function update(Request $request, $id)
    {
        $donasi = Donation::with(['moota'])->findOrFail($id);

        $validated = $request->validate([
            'amount' => 'required|numeric',
            'donatur' => 'nullable|array',
            'donatur.id' => 'nullable|exists:donaturs,id',
            'donatur.name' => 'nullable|string|max:255',
            'program' => 'nullable|array',
            'program.id' => 'nullable|exists:programs,id',
            'penghubung' => 'nullable|array',
            'penghubung.id' => 'nullable|exists:users,id',
        ]);

        // Handle donatur
        $donaturId = null;
        if (!empty($validated['donatur']['id'])) {
            $donaturId = $validated['donatur']['id'];
        } elseif (!empty($validated['donatur']['name'])) {
            $donatur = \App\Models\Donatur::whereRaw('LOWER(name) = ?', [strtolower($validated['donatur']['name'])])->first();
            if (!$donatur) {
                $donatur = \App\Models\Donatur::create(['name' => $validated['donatur']['name']]);
            }
            $donaturId = $donatur->id;
        }

        // Handle program
        $programId = $validated['program']['id'] ?? null;

        // Handle penghubung
        $penghubungId = $validated['penghubung']['id'] ?? $donasi->user_id;

        // Update the Donation
        $donasi->update([
            'donatur_id' => $donaturId,
            'program_id' => $programId,
            'user_id' => $penghubungId,
            'updated_by' => Auth::user()->id,
        ]);

        // Update Moota record if not from webhook (only amount can be updated for manual entries)
        if ($donasi->moota && $donasi->moota->creation === 'manual') {
            $donasi->moota->update([
                'amount' => $validated['amount'],
                'person' => $donaturId ? \App\Models\Donatur::find($donaturId)->name : null,
            ]);
        }

        return response()->json(['success' => true, 'donasi' => $donasi]);
    }

    public function destroy($id)
    {
        $donasi = Donation::with(['moota'])->findOrFail($id);

        // Prevent deletion of webhook-created donations
        if ($donasi->moota && $donasi->moota->creation === 'webhook') {
            return response()->json(['success' => false, 'message' => 'Cannot delete webhook-created donations'], 403);
        }

        // Delete associated moota record if it's manual
        if ($donasi->moota && $donasi->moota->creation === 'manual') {
            $donasi->moota->delete();
        }

        $donasi->delete();
        return response()->json(['success' => true]);
    }

    /**
     * Return paginated Donation data for AJAX table pagination.
     */
    public function ajaxDonasi(Request $request)
    {
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');
        $query = Donation::with(['program', 'donatur', 'staff', 'moota']);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->whereHas('donatur', function ($subq) use ($search) {
                    $subq->where('name', 'like', "%$search%");
                })
                    ->orWhereHas('program', function ($subq) use ($search) {
                        $subq->where('name', 'like', "%$search%");
                    })
                    ->orWhereHas('moota', function ($subq) use ($search) {
                        $subq->where('amount', 'like', "%$search%")
                            ->orWhere('note', 'like', "%$search%");
                    });
            });
        }

        $donations = $query->orderBy('created_at', 'desc')->paginate($perPage);

        // Format data for table
        $donations->getCollection()->transform(function ($item) {
            return [
                'id' => $item->id,
                'amount' => $item->moota ? $item->moota->amount : 0,
                'program' => $item->program ? $item->program->name : '-',
                'donatur' => $item->donatur ? $item->donatur->name : '-',
                'penghubung' => $item->staff ? $item->staff->name : '-',
                'date' => $item->created_at ? $item->created_at->format('Y-m-d H:i') : null,
                'description' => $item->description,
            ];
        });
        return response()->json($donations);
    }

    /**
     * Return the Donasi table partial for AJAX HTML requests.
     */
    public function tablePartial(Request $request)
    {
        $donations = $this->getFilteredDonations($request);

        // Pass paginator as $donasi for compatibility with table partial
        $tableHtml = view('dashboard.donasi.table', ['donasi' => $donations])->render();
        $paginationHtml = view('components.pagination', ['paginator' => $donations])->render();

        // Wrap the table and pagination in container divs with their respective IDs
        $html = '<div id="donasi-table">' . $tableHtml . '</div>';
        $html .= '<div class="mt-1 px-4 py-3 rounded-b-lg" id="pagination-links">' . $paginationHtml . '</div>';

        return $html;
    }
}
