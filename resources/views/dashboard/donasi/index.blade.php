@extends('layouts.app')

@section('title', 'Donasi Management')
@section('panel-type', 'Donasi Panel')

@section('content')
<div x-data="donasiManagement()"
     x-cloak
     @view-donasi.window="viewDonasi($event.detail.id)"
     @edit-donasi.window="editDonasi($event.detail.id)"
     @delete-donasi.window="confirmDelete($event.detail.id)">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">Donasi Management</h2>
        @if(auth()->user()->isAdmin())
        <button @click="openCreateDialog()" class="bg-blue-500 text-white text-sm px-4 py-2 rounded-lg hover:bg-blue-600 cursor-pointer">
            New
        </button>
        @endif
    </div>

    <!-- Filter Form -->
    @include('dashboard.donasi.filter')

    <!-- Donasi Table with Loading Overlay -->
    <div class="relative">
        <div x-show="loading"
             class="absolute inset-0 backdrop-blur-xs flex items-center justify-center z-10">
            <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span class="text-gray-500 text-sm">Loading...</span>
            </div>
        </div>
        <div id="donasi-table-container" x-ref="donasiTableContainer">
            <div id="donasi-table">
                @include('dashboard.donasi.table', ['donasi' => $donasi ?? $donations ?? []])
            </div>
            @php $paginator = $donasi ?? $donations ?? null; @endphp
            @if($paginator instanceof \Illuminate\Pagination\LengthAwarePaginator)
                <div class="mt-1 px-4 py-3 rounded-b-lg" id="pagination-links">
                    <x-pagination :paginator="$paginator" />
                </div>
            @endif
        </div>
    </div>

    <!-- Create/Edit Dialog -->
    @include('dashboard.donasi.form')

    <!-- View Dialog -->
    @include('dashboard.donasi.view')

    <!-- Delete Confirmation Modal -->
    <x-delete-confirmation 
        showVariable="showDeleteDialog"
        title="Confirm Deletion"
        message="Are you sure you want to delete this donasi? This action cannot be undone."
        deleteMethod="confirmDelete(currentDonasiId)"
        cancelMethod="showDeleteDialog = false"
    />
</div>
@endsection

@push('scripts')
<script>
function donasiManagement() {
    return {
        showDialog: false,
        showViewDialog: false,
        showDeleteDialog: false,
        formData: {
            amount: '',
            donatur: '',
            program: '',
            status: 'pending',
            penghubung: '',
            image_url: null,
            image_path: null,
            is_webhook: false
        },
        selectedFile: null,
        errors: {},
        viewData: {
            image_url: null,
            donor_name: '',
            amount: 0,
            program_name: '',
            status: '',
            created_at: ''
        },
        selectedDonatur: {
            name: ''
        },
        dialogTitle: '',
        loading: false,
        currentDonasiId: null,
        filters: {
            donatur: '',
            program: '',
            penghubung: '',
            manager: '',
            creation: ''
        },
        page: 1,
        perPage: 10,
        lastPage: 1,
        openCreateDialog() {
            this.dialogTitle = 'New Donasi';
            // Reset form data
            this.formData = {
                amount: '',
                donatur: '',
                program: '',
                status: 'pending',
                penghubung: '',
                image_url: null,
                image_path: null,
                is_webhook: false
            };
            this.currentDonasiId = null;
            this.showDialog = true;
        },
        closeDialog() {
            this.showDialog = false;
        },
        async viewDonasi(id) {
            this.currentDonasiId = id;

            try {
                const response = await fetch(`/dashboard/donasi/${id}`);
                const data = await response.json();

                // Populate view data
                this.viewData = {
                    image_url: data.proof_image,
                    donor_name: data.donatur ? data.donatur.name : '-',
                    amount: data.moota ? data.moota.amount : 0,
                    program_name: data.program ? data.program.name : '-',
                    status: 'pending', // Default status since it's not in the donations table
                    created_at: data.created_at,
                    moota: data.moota
                };

                this.showViewDialog = true;
            } catch (error) {
                console.error('Error loading donation data:', error);
                alert('Error loading donation data');
            }
        },
        async editDonasi(id) {
            this.currentDonasiId = id;
            this.dialogTitle = 'Edit Donasi';

            try {
                const response = await fetch(`/dashboard/donasi/${id}/edit`);
                const data = await response.json();

                // Populate form data
                this.formData.amount = data.moota ? data.moota.amount : 0;
                this.formData.donatur = data.donatur ? { id: data.donatur.id, name: data.donatur.name } : '';
                this.formData.program = data.program ? { id: data.program.id, name: data.program.name } : '';
                this.formData.penghubung = data.staff ? { id: data.staff.id, name: data.staff.name } : '';
                this.formData.is_webhook = data.moota && data.moota.creation === 'webhook';

                this.showDialog = true;
            } catch (error) {
                console.error('Error loading donation data:', error);
                alert('Error loading donation data');
            }
        },
        confirmDelete(id) {
            this.currentDonasiId = id;
            this.showDeleteDialog = true;
        },
        applyFilters() {
            const params = new URLSearchParams();
            if (this.filters.donatur) params.set('donatur', this.filters.donatur);
            if (this.filters.program) params.set('program', this.filters.program);
            if (this.filters.penghubung) params.set('penghubung', this.filters.penghubung);
            if (this.filters.manager) params.set('manager', this.filters.manager);
            if (this.filters.creation) params.set('creation', this.filters.creation);
            params.set('page', 1);
            
            // Update URL with filter parameters for bookmarking/sharing
            const newUrl = `${window.location.pathname}?${params.toString()}`;
            window.history.pushState({}, '', newUrl);
            
            this.loadPage(`/dashboard/donasi/table?${params.toString()}`);
        },
        gotoPage(page) {
            if (page < 1 || page > this.lastPage) return;
            const params = new URLSearchParams();
            if (this.filters.donatur) params.set('donatur', this.filters.donatur);
            if (this.filters.program) params.set('program', this.filters.program);
            if (this.filters.penghubung) params.set('penghubung', this.filters.penghubung);
            if (this.filters.creation) params.set('creation', this.filters.creation);
            params.set('page', page);
            this.loadPage(`/dashboard/donasi/table?${params.toString()}`);
        },
        setupPagination() {
            document.addEventListener('click', (e) => {
                const element = e.target.closest('#pagination-links a');
                if (element) {
                    e.preventDefault();
                    this.loadPage(element.href);
                }
            });
        },
        async loadPage(url) {
            try {
                this.loading = true;
                this.currentPageUrl = url;
                const response = await fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const html = await response.text();
                
                // Create a temporary container to parse the HTML response
                const tempContainer = document.createElement('div');
                tempContainer.innerHTML = html;
                
                // Update the table content
                const tableContent = tempContainer.querySelector('#donasi-table');
                if (tableContent) {
                    document.querySelector('#donasi-table').innerHTML = tableContent.innerHTML;
                }
                
                // Update the pagination links
                const paginationLinks = tempContainer.querySelector('#pagination-links');
                if (paginationLinks) {
                    const currentPaginationLinks = document.querySelector('#pagination-links');
                    if (currentPaginationLinks) {
                        currentPaginationLinks.innerHTML = paginationLinks.innerHTML;
                    }
                    
                    const lastPageEl = paginationLinks.querySelector('[data-last-page]');
                    if (lastPageEl) {
                        this.lastPage = parseInt(lastPageEl.getAttribute('data-last-page'), 10);
                    }
                }
                
                // Initialize Alpine.js on the updated content
                Alpine.initTree(document.querySelector('#donasi-table-container'));
            } catch (error) {
                console.error('Error loading page:', error);
            } finally {
                this.loading = false;
            }
        },
        init() {
            this.setupPagination();
            this.currentPageUrl = window.location.pathname + window.location.search;
            // Restore filters from URL if present
            const urlParams = new URLSearchParams(window.location.search);
            this.filters.donatur = urlParams.get('donatur') || '';
            this.filters.program = urlParams.get('program') || '';
            this.filters.penghubung = urlParams.get('penghubung') || '';
            this.filters.manager = urlParams.get('manager') || '';
            this.filters.creation = urlParams.get('creation') || '';
            
            // Apply filters if any are set in the URL
            if (this.filters.donatur || this.filters.program || this.filters.penghubung || this.filters.manager || this.filters.creation) {
                this.applyFilters();
            }
            
            // Listen for custom filter events if needed
            window.addEventListener('close-all-dialogs', () => {
                this.closeDialog && this.closeDialog();
                this.showViewDialog = false;
                this.showDeleteDialog = false;
            });
        }
    };
}
document.addEventListener('alpine:init', () => {
    Alpine.data('donasiManagement', donasiManagement);
});
</script>
@endpush
