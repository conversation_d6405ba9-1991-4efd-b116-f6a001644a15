<x-modal-dialog :show-variable="'showDialog'" :max-width="'lg'" :close-method="'closeDialog()'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900" x-text="dialogTitle"></h3>
        <button @click="closeDialog()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    <!-- (Optional) Image preview for donation proof (if applicable) -->
    <div x-show="formData.image_url && !selectedFile" class="mb-4 relative">
        <img :src="formData.image_url" class="w-full h-48 object-cover rounded-md" alt="Donation Proof">
        <button @click="formData.image_url = null; formData.image_path = null" 
                class="absolute top-2 right-2 bg-white/80 hover:bg-white p-1 rounded-full text-gray-700 hover:text-gray-900 focus:outline-none shadow-md">
            <x-icon name="x" width="20" height="20" />
        </button>
    </div>
    <div x-show="selectedFile" class="mb-4 relative">
        <img x-ref="imagePreview" class="w-full h-48 object-cover rounded-md" alt="Selected Image">
        <button @click="selectedFile = null; $refs.fileInput.value = ''" 
                class="absolute top-2 right-2 bg-white/80 hover:bg-white p-1 rounded-full text-gray-700 hover:text-gray-900 focus:outline-none shadow-md">
            <x-icon name="x" width="20" height="20" />
        </button>
    </div>
    <form @submit.prevent="submitForm()">
        <div class="space-y-4">
            <!-- Amount field -->
            <div>
                <label for="amount" class="block text-sm text-gray-500 mb-1">Amount <span class="text-red-500">*</span></label>
                <input type="number" id="amount" x-model="formData.amount" required
                       x-bind:disabled="formData.is_webhook"
                       class="block w-full h-[38px] px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
                       x-bind:class="{'border-red-300 focus:ring-red-500': errors.amount, 'bg-gray-100 cursor-not-allowed': formData.is_webhook}">
                <div x-show="errors.amount" x-text="errors.amount" class="mt-1 text-sm text-red-500"></div>
                <div x-show="formData.is_webhook" class="mt-1 text-sm text-gray-500">Amount cannot be changed for webhook donations</div>
            </div>
            <!-- Donatur field (not required, single object) -->
            <div>
                <label for="donatur-form-input" class="block text-sm text-gray-500 mb-1">Donatur</label>
                <div @input="formData.donatur = $event.detail" id="donatur-form-input-wrapper">
                    <x-donatur-form :initial-value="null" :multiple="false" :required="false" id="donatur-form-input" class="h-[38px]" />
                </div>
                <div x-show="errors.donatur" x-text="errors.donatur" class="mt-1 text-sm text-red-500"></div>
            </div>
            <!-- Program field (single select, not required) -->
            <div>
                <label for="program-form-input" class="block text-sm text-gray-500 mb-1">Program</label>
                <div @input="formData.program = $event.detail" id="program-form-input-wrapper">
                    <x-program-form :initial-value="null" :multiple="false" :required="false" id="program-form-input" class="h-[38px]" />
                </div>
                <div x-show="errors.program" x-text="errors.program" class="mt-1 text-sm text-red-500"></div>
            </div>
            <!-- Status (hidden, default to pending) -->
            <input type="hidden" x-model="formData.status" value="pending" />
            <!-- Penghubung field (single select, not required) -->
            <div>
                <label for="penghubung-form-input" class="block text-sm text-gray-500 mb-1">Penghubung</label>
                <div @user-selected="formData.penghubung = $event.detail.user" @user-cleared="formData.penghubung = ''" id="penghubung-form-input-wrapper">
                    <x-penghubung-form :initial-value="null" :multiple="false" :required="false" id="penghubung-form-input" class="h-[38px]" />
                </div>
                <div x-show="errors.penghubung" x-text="errors.penghubung" class="mt-1 text-sm text-red-500"></div>
            </div>
        </div>
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-100 mt-4">
            <button type="submit" class="flex px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 shadow-sm transition duration-150" x-bind:disabled="loading">
                <span x-show="!loading">Save</span>
                <span x-show="loading">Saving...</span>
            </button>
        </div>
    </form>
</x-modal-dialog>
