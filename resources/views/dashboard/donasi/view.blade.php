<x-modal-dialog :show-variable="'showViewDialog'" :max-width="'lg'" :close-method="'showViewDialog = false'">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-900">Donation Details</h3>
        <button @click="showViewDialog = false" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <x-icon name="x" width="24" height="24" />
        </button>
    </div>
    <!-- (Optional) Image preview for donation proof (if available) -->
    <div x-show="viewData.image_url" class="mb-4">
        <img :src="viewData.image_url" class="w-full h-48 object-cover rounded-md" alt="Donation Proof">
    </div>
    <div class="space-y-4">
        <div>
            <label class="block text-sm text-gray-500 mb-1">Donor</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.donor_name"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Amount</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="formatCurrency(viewData.amount)"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Program</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.program_name"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Status</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900 capitalize"
               x-bind:class="{
                   'text-blue-700': viewData.status === 'pending',
                   'text-green-700': viewData.status === 'success',
                   'text-red-700': viewData.status === 'failed',
                   'text-gray-700': viewData.status === 'draft'
               }"
               x-text="viewData.status === 'pending' ? 'Pending' : (viewData.status === 'success' ? 'Success' : (viewData.status === 'failed' ? 'Failed' : 'Draft'))"></p>
        </div>
        <div>
            <label class="block text-sm text-gray-500 mb-1">Date</label>
            <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.created_at ? formatDateIndonesian(viewData.created_at) : '-' "></p>
        </div>

        <!-- Moota Information (only show for webhook donations) -->
        <div x-show="viewData.moota && viewData.moota.creation === 'webhook'" class="border-t border-gray-200 pt-4 mt-4">
            <h4 class="text-lg font-medium text-gray-900 mb-3">Moota Information</h4>
            <div class="space-y-3">
                <div>
                    <label class="block text-sm text-gray-500 mb-1">Person</label>
                    <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.moota && viewData.moota.person ? viewData.moota.person : '-'"></p>
                </div>
                <div>
                    <label class="block text-sm text-gray-500 mb-1">Note</label>
                    <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.moota && viewData.moota.note ? viewData.moota.note : '-'"></p>
                </div>
                <div>
                    <label class="block text-sm text-gray-500 mb-1">Transaction ID</label>
                    <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.moota && viewData.moota.trx_id ? viewData.moota.trx_id : '-'"></p>
                </div>
                <div>
                    <label class="block text-sm text-gray-500 mb-1">Mutation ID</label>
                    <p class="block w-full px-3 py-2 rounded-md bg-gray-50 text-gray-900" x-text="viewData.moota && viewData.moota.mutation_id ? viewData.moota.mutation_id : '-'"></p>
                </div>
            </div>
        </div>
    </div>
    <div class="flex justify-end mt-4">
        <button @click="showViewDialog = false" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">Close</button>
    </div>
</x-modal-dialog>
